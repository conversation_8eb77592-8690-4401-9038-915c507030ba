import React, { useState, useEffect, useRef } from 'react';

// Add CSS for range slider styling
const sliderCSS = `
  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #2D8C88;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);
  }

  input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #2D8C88;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(45, 140, 136, 0.3);
  }

  input[type="range"]::-webkit-slider-track {
    background: #e0e0e0;
    height: 6px;
    border-radius: 3px;
  }

  input[type="range"]::-moz-range-track {
    background: #e0e0e0;
    height: 6px;
    border-radius: 3px;
    border: none;
  }

  input[type="range"]:focus {
    outline: none;
  }

  input[type="range"]:focus::-webkit-slider-thumb {
    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);
  }

  input[type="range"]:focus::-moz-range-thumb {
    box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.2);
  }
`;

// Inject CSS (only once)
if (typeof document !== 'undefined' && !document.getElementById('wrist-size-slider-styles')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'wrist-size-slider-styles';
  styleElement.textContent = sliderCSS;
  document.head.appendChild(styleElement);
}

const Tryon = ({ onBackToHome }) => {
  // Refs for DOM elements
  const videoRef = useRef(null);
  const capturedImageRef = useRef(null);
  const canvasRef = useRef(null);

  // State variables
  const [isCaptured, setIsCaptured] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [isRightHand, setIsRightHand] = useState(false);
  const [showProductSelection, setShowProductSelection] = useState(false);
  const [activeTab, setActiveTab] = useState('Watches');
  const [showHandGuide, setShowHandGuide] = useState(true);
  const [isMobile, setIsMobile] = useState(false);

  // Watch size customization state
  const [userGender, setUserGender] = useState('men'); // 'men' or 'women'
  const [userWristSize, setUserWristSize] = useState(64); // Default men's wrist size in mm

  // Autocapture state variables
  const [isAutoCaptureEnabled, setIsAutoCaptureEnabled] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [isHandInPosition, setIsHandInPosition] = useState(false);
  const [isCountdownActive, setIsCountdownActive] = useState(false);

  // Detect mobile device and set viewport height for Chrome mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    // Fix viewport height for Chrome mobile
    const setVH = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    checkMobile();
    setVH();

    window.addEventListener('resize', () => {
      checkMobile();
      setVH();
    });

    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        setVH();
      }, 100);
    });

    return () => {
      window.removeEventListener('resize', checkMobile);
      window.removeEventListener('orientationchange', setVH);
    };
  }, []);

  // Realistic sizing configuration
  // Average adult wrist circumference: 165mm (men), 155mm (women)
  // Average wrist width when viewed from top: ~55-65mm
  // SVG guide circle represents the wrist area (120px diameter in 800px viewBox)
  // This means the circle represents approximately 60mm in real life

  // Default wrist sizes by gender
  const DEFAULT_WRIST_SIZES = {
    men: 64,    // mm - default men's wrist size
    women: 54   // mm - default women's wrist size
  };

  const SVG_WRIST_CIRCLE_DIAMETER = 120; // SVG circle diameter in viewBox units
  const SVG_VIEWBOX_WIDTH = 800; // SVG viewBox width
  const SVG_VIEWBOX_HEIGHT = 600; // SVG viewBox height

  // Size configuration - Increased for better visibility
  const WATCH_WIDTH = 25; // percentage of container width
  const BRACELET_WIDTH = 15; // percentage of container width
  const WATCH_HEIGHT = 38; // percentage of container height
  const BRACELET_HEIGHT = 55; // percentage of container height - INCREASED for better visibility

  // Test: Try changing WATCH_HEIGHT to 50 to see a much taller watch
  // Test: Try changing WATCH_HEIGHT to 15 to see a shorter watch
  // Test: Try changing BRACELET_HEIGHT to 70 to see a much taller bracelet
  // Test: Try changing BRACELET_HEIGHT to 20 to see a shorter bracelet
  // Note: Scale is now dynamic - smaller height = smaller scale, larger height = larger scale

  // Calculate realistic watch sizing based on actual dimensions and user's wrist size
  const calculateWatchDimensions = (watch, containerWidth, containerHeight) => {
    // Get the default wrist size for the current gender
    const defaultWristSize = DEFAULT_WRIST_SIZES[userGender];

    // Calculate relative scaling factor based on user's wrist size vs default
    const wristSizeRatio = userWristSize / defaultWristSize;

    // Calculate the scale factor from real world to SVG coordinates using user's wrist size
    const mmToSvgScale = SVG_WRIST_CIRCLE_DIAMETER / userWristSize;

    // Convert watch dimensions to SVG units
    const watchWidthSvg = watch.totalWidth * mmToSvgScale;
    const watchHeightSvg = watch.totalHeight * mmToSvgScale;
    const dialDiameterSvg = watch.dialDiameter * mmToSvgScale;

    // Convert SVG units to container percentages
    const watchWidthPercent = (watchWidthSvg / SVG_VIEWBOX_WIDTH) * 100;
    const watchHeightPercent = (watchHeightSvg / SVG_VIEWBOX_HEIGHT) * 100;
    const dialDiameterPercent = (dialDiameterSvg / SVG_VIEWBOX_WIDTH) * 100;

    // Calculate positioning - watches should be positioned on the wrist circle
    // SVG circle is at cx="400" cy="300" (center of the hand area)
    const positionX = 50; // Center horizontally (400/800 = 50%)
    const positionY = 50; // Center vertically (300/600 = 50%)

    return {
      width: Math.max(watchWidthPercent, 8), // Minimum 8% for visibility
      height: Math.max(watchHeightPercent, 10), // Minimum 10% for visibility
      dialDiameter: dialDiameterPercent,
      positionX,
      positionY,
      scale: Math.min(watchWidthPercent / 15, watchHeightPercent / 20) * wristSizeRatio, // Dynamic scale with wrist size adjustment
      realWidth: watch.totalWidth,
      realHeight: watch.totalHeight,
      caseDiameter: watch.caseDiameter,
      wristSizeRatio // Include ratio for additional scaling if needed
    };
  };

  // Calculate watch position based on hand orientation and anatomy
  const getWatchPosition = (watchData, isRightHand) => {
    const baseDimensions = calculateWatchDimensions(watchData, 400, 600);

    // Adjust position based on hand orientation
    // Watches are typically worn on the top of the wrist
    let adjustedX = baseDimensions.positionX;
    let adjustedY = baseDimensions.positionY - 2; // Slightly higher on the wrist

    // For right hand, watch might be positioned slightly differently
    if (isRightHand) {
      adjustedX = baseDimensions.positionX + 1; // Slight adjustment for right hand
    }

    // Adjust for different watch types
    switch (watchData.type) {
      case 'smartwatch':
        // Smart watches are often worn higher on the wrist
        adjustedY -= 1;
        break;
      case 'luxury':
        // Luxury watches might be positioned more precisely
        adjustedY -= 0.5;
        break;
      case 'sport':
        // Sport watches might be worn slightly looser
        adjustedY += 0.5;
        break;
      default:
        break;
    }

    return {
      ...baseDimensions,
      positionX: adjustedX,
      positionY: adjustedY
    };
  };
  // Realistic watch data with actual dimensions (in millimeters)
  const watches = [
    {
      name: "Classic Black",
      path: "watches/watch_1.png",
      // Rolex Submariner style - 40mm case
      caseDiameter: 41, // mm
      caseThickness: 12.5, // mm
      totalWidth: 42, // mm (including crown)
      totalHeight: 47, // mm (lug to lug)
      dialDiameter: 31, // mm (visible dial)
      type: "dress",
      dialSize: 40
    },
    {
      name: "Silver Chrono",
      path: "watches/watch_2.png",
      // Omega Speedmaster style - 42mm case
      caseDiameter: 42, // mm
      caseThickness: 13.2, // mm
      totalWidth: 44, // mm
      totalHeight: 48.5, // mm
      dialDiameter: 33, // mm
      type: "sport",
      dialSize: 42
    },
    {
      name: "Gold Luxury",
      path: "watches/watch_3.png",
      // Patek Philippe Calatrava style - 38mm case
      caseDiameter: 39, // mm
      caseThickness: 8.5, // mm
      totalWidth: 39, // mm
      totalHeight: 45, // mm
      dialDiameter: 30, // mm
      type: "luxury",
      dialSize: 38
    },
    {
      name: "Sport Blue",
      path: "watches/watch_6.png",
      // Apple Watch style - 44mm case
      caseDiameter: 41, // mm (width)
      caseThickness: 10.7, // mm
      totalWidth: 44, // mm
      totalHeight: 38, // mm (height - rectangular)
      dialDiameter: 35, // mm (screen diagonal)
      type: "smartwatch",
      dialSize: 44
    },
    {
      name: "Minimalist",
      path: "watches/watch_5.png",
      // Daniel Wellington style - 36mm case
      caseDiameter: 36, // mm
      caseThickness: 6, // mm
      totalWidth: 37, // mm
      totalHeight: 43, // mm
      dialDiameter: 28, // mm
      type: "minimalist",
      dialSize: 36
    },
    {
      name: "Rose Gold",
      path: "watches/watch_4.png",
      // Michael Kors style - 39mm case
      caseDiameter: 44, // mm
      caseThickness: 11, // mm
      totalWidth: 41, // mm
      totalHeight: 46, // mm
      dialDiameter: 31, // mm
      type: "fashion",
      dialSize: 41
    }
  ];

  const bracelets = [
    { name: "Silver Chain", path: "bracelets/bracelet_1.png" },
    { name: "Gold Bangle", path: "bracelets/bracelet_2.png" },
    { name: "Leather Wrap", path: "bracelets/bracelet_3.png" },
    { name: "Diamond Tennis", path: "bracelets/bracelet_4.png" },
    { name: "Beaded Stone", path: "bracelets/bracelet_5.png" },
    { name: "Charm Bracelet", path: "bracelets/bracelet_6.png" }
  ];

  // Initialize camera
  const initCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment',
          width: { ideal: 1920 },
          height: { ideal: 1080 }
        }
      });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (err) {
      console.error("Error accessing camera:", err);
      // Demo mode fallback
      if (capturedImageRef.current) {
        capturedImageRef.current.src = "sample-hand.jpg";
        capturedImageRef.current.style.display = "block";
      }
      console.log("Camera not available - demo mode");
      setIsCaptured(true);
      setShowProductSelection(true);
    }
  };

  // Capture current frame
  const captureFrame = () => {
    if (!videoRef.current || !canvasRef.current) return null;

    const canvas = canvasRef.current;
    const video = videoRef.current;
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    const ctx = canvas.getContext('2d');
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    return canvas.toDataURL('image/png');
  };

  // Improved background remover that preserves light colors
  const removeBackground = (imgElement, productType = 'watch') => {
    const img = new Image();
    img.crossOrigin = 'anonymous';

    img.onload = function() {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;
      ctx.drawImage(img, 0, 0);

      try {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        const width = canvas.width;
        const height = canvas.height;

        // First pass: Identify edge pixels to preserve product boundaries
        const edgePixels = new Set();
        for (let y = 1; y < height - 1; y++) {
          for (let x = 1; x < width - 1; x++) {
            const idx = (y * width + x) * 4;
            const r = data[idx];
            const g = data[idx + 1];
            const b = data[idx + 2];

            // Check surrounding pixels for edge detection
            let isEdge = false;
            for (let dy = -1; dy <= 1; dy++) {
              for (let dx = -1; dx <= 1; dx++) {
                if (dx === 0 && dy === 0) continue;
                const neighborIdx = ((y + dy) * width + (x + dx)) * 4;
                const nr = data[neighborIdx];
                const ng = data[neighborIdx + 1];
                const nb = data[neighborIdx + 2];

                // If there's a significant color difference, it's an edge
                const colorDiff = Math.abs(r - nr) + Math.abs(g - ng) + Math.abs(b - nb);
                if (colorDiff > 60) {
                  isEdge = true;
                  break;
                }
              }
              if (isEdge) break;
            }

            if (isEdge) {
              edgePixels.add(idx / 4);
            }
          }
        }

        // Second pass: Remove only white background while preserving all product colors
        for (let i = 0; i < data.length; i += 4) {
          const r = data[i];
          const g = data[i + 1];
          const b = data[i + 2];
          const pixelIndex = i / 4;

          // Calculate color properties
          const brightness = (r + g + b) / 3;

          // Check if pixel is near edges (preserve product boundaries)
          const isNearEdge = edgePixels.has(pixelIndex);

          // Only remove pure white or near-white pixels that aren't edges
          const isPureWhite = (
            r > 245 &&
            g > 245 &&
            b > 245 &&
            Math.abs(r - g) < 10 &&
            Math.abs(g - b) < 10 &&
            !isNearEdge
          );

          // Remove background if it's pure white and not an edge
          if (isPureWhite) {
            data[i + 3] = 0; // Make fully transparent
          } else if (brightness > 240 && !isNearEdge) {
            // For very bright but not pure white, reduce opacity slightly
            data[i + 3] = Math.max(0, data[i + 3] - 50);
          }
        }

        ctx.putImageData(imageData, 0, 0);
        imgElement.src = canvas.toDataURL('image/png');

        // Apply product-specific styling that preserves all colors
        imgElement.style.filter = 'none';
        imgElement.style.mixBlendMode = 'normal';
        imgElement.style.opacity = '1';

      } catch (e) {
        console.warn('Canvas operation failed:', e);
        // Fallback styling that preserves all colors
        imgElement.style.filter = 'none';
        imgElement.style.mixBlendMode = 'normal';
        imgElement.style.opacity = '1';
      }
    };

    img.onerror = function() {
      console.warn('Image loading failed');
      // Fallback styling
      imgElement.style.filter = 'none';
      imgElement.style.mixBlendMode = 'normal';
      imgElement.style.opacity = '1';
    };

    img.src = imgElement.src;
  };

  // Placeholder for hand detection
  const detectHandOrientation = (imageData) => {
    // Simple heuristic for demo purposes
    return Math.random() > 0.5;
  };

  // Detect if arm and wrist are within the SVG guide area
  const detectHandInPosition = () => {
    if (!videoRef.current || !canvasRef.current) return false;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw current video frame
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Get the video container dimensions to calculate the guide area
    const videoContainer = video.parentElement;
    const containerRect = videoContainer.getBoundingClientRect();

    // Calculate the actual video display area (accounting for object-fit: cover)
    const videoAspect = video.videoWidth / video.videoHeight;
    const containerAspect = containerRect.width / containerRect.height;

    let displayWidth, displayHeight, offsetX, offsetY;

    if (videoAspect > containerAspect) {
      // Video is wider - height fills container, width is cropped
      displayHeight = containerRect.height;
      displayWidth = displayHeight * videoAspect;
      offsetX = (displayWidth - containerRect.width) / 2;
      offsetY = 0;
    } else {
      // Video is taller - width fills container, height is cropped
      displayWidth = containerRect.width;
      displayHeight = displayWidth / videoAspect;
      offsetX = 0;
      offsetY = (displayHeight - containerRect.height) / 2;
    }

    // Calculate the guide areas in canvas coordinates
    // SVG viewBox is 800x600
    // Main rectangle: x="320" y="150" width="160" height="300" (wrist/forearm area)
    // Circle: cx="400" cy="300" r="60" (hand area)

    const scaleX = canvas.width / displayWidth;
    const scaleY = canvas.height / displayHeight;

    // Wrist/forearm area (rectangle)
    const rectX = Math.max(0, ((320 / 800) * displayWidth - offsetX) * scaleX);
    const rectY = Math.max(0, ((150 / 600) * displayHeight - offsetY) * scaleY);
    const rectWidth = Math.min(canvas.width - rectX, ((160 / 800) * displayWidth) * scaleX);
    const rectHeight = Math.min(canvas.height - rectY, ((300 / 600) * displayHeight) * scaleY);

    // Hand area (circle)
    const circleX = Math.max(0, ((340 / 800) * displayWidth - offsetX) * scaleX); // Adjusted for circle bounds
    const circleY = Math.max(0, ((240 / 600) * displayHeight - offsetY) * scaleY); // Adjusted for circle bounds
    const circleWidth = Math.min(canvas.width - circleX, ((120 / 800) * displayWidth) * scaleX); // Circle diameter
    const circleHeight = Math.min(canvas.height - circleY, ((120 / 600) * displayHeight) * scaleY); // Circle diameter

    try {
      // Check wrist/forearm area (rectangle)
      const rectImageData = ctx.getImageData(rectX, rectY, rectWidth, rectHeight);
      const rectData = rectImageData.data;

      // Check hand area (circle approximation)
      const circleImageData = ctx.getImageData(circleX, circleY, circleWidth, circleHeight);
      const circleData = circleImageData.data;

      let rectSkinPixels = 0;
      let rectTotalPixels = 0;
      let circleSkinPixels = 0;
      let circleTotalPixels = 0;

      // Enhanced skin tone detection
      const isSkinTone = (r, g, b) => {
        // Multiple skin tone ranges to cover different skin colors
        const condition1 = r > 95 && g > 40 && b > 20 && r > g && r > b && Math.abs(r - g) > 15;
        const condition2 = r > 220 && g > 210 && b > 170 && Math.abs(r - g) < 15 && Math.abs(g - b) < 15;
        const condition3 = r > 120 && g > 80 && b > 50 && r > g && g > b;
        const condition4 = r > 180 && g > 120 && b > 90 && r > g && g >= b;

        return condition1 || condition2 || condition3 || condition4;
      };

      // Analyze rectangle area (wrist/forearm)
      for (let i = 0; i < rectData.length; i += 4) {
        const r = rectData[i];
        const g = rectData[i + 1];
        const b = rectData[i + 2];

        if (isSkinTone(r, g, b)) {
          rectSkinPixels++;
        }
        rectTotalPixels++;
      }

      // Analyze circle area (hand)
      for (let i = 0; i < circleData.length; i += 4) {
        const r = circleData[i];
        const g = circleData[i + 1];
        const b = circleData[i + 2];

        if (isSkinTone(r, g, b)) {
          circleSkinPixels++;
        }
        circleTotalPixels++;
      }

      // Calculate skin ratios
      const rectSkinRatio = rectSkinPixels / rectTotalPixels;
      const circleSkinRatio = circleSkinPixels / circleTotalPixels;

      // Both wrist/forearm AND hand areas must have sufficient skin tone presence
      // Wrist area needs at least 20% skin tone (forearm/wrist)
      // Hand area needs at least 25% skin tone (hand/fingers)
      const wristInPosition = rectSkinRatio > 0.20;
      const handInPosition = circleSkinRatio > 0.25;

      return wristInPosition && handInPosition;

    } catch (error) {
      console.warn('Hand detection error:', error);
      return false;
    }
  };

  // Apply product to watch position with dynamic scaling
  const applyProductToWatchPosition = (productPath, productType) => {
    // Clear any existing product first
    setSelectedProduct(null);

    // Find the watch data for dial size
    const watchData = watches.find(w => w.path === productPath);

    // Small delay to ensure the old product is removed before adding new one
    setTimeout(() => {
      setSelectedProduct({
        path: productPath,
        dialSize: watchData?.dialSize || 40 // Default to 40mm if not found
      });
    }, 50);
  };

  // Handle capture button click
  const handleCapture = () => {
    if (!isCaptured) {
      const capturedDataUrl = captureFrame();
      if (capturedImageRef.current && capturedDataUrl) {
        capturedImageRef.current.src = capturedDataUrl;
        capturedImageRef.current.style.display = 'block';
      }
      setIsCaptured(true);
      setShowWristSizeInput(true); // Always show wrist size input
      setShowProductSelection(true); // Also show products immediately
      setShowHandGuide(false);

      // Detect hand orientation
      if (canvasRef.current && videoRef.current) {
        const canvas = canvasRef.current;
        const ctx = canvas.getContext('2d');
        canvas.width = videoRef.current.videoWidth;
        canvas.height = videoRef.current.videoHeight;
        ctx.drawImage(videoRef.current, 0, 0);
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        setIsRightHand(detectHandOrientation(imageData));
      }
    } else {
      setShowProductSelection(true);
    }
  };

  // Handle back button click
  const handleBack = () => {
    if (capturedImageRef.current) {
      capturedImageRef.current.style.display = 'none';
    }
    setIsCaptured(false);
    setSelectedProduct(null);
    setIsRightHand(false);
    setShowProductSelection(false);
    setShowHandGuide(true);
  };

  // Handle gender selection
  const handleGenderChange = (gender) => {
    setUserGender(gender);
    setUserWristSize(DEFAULT_WRIST_SIZES[gender]);
  };

  // Handle wrist size change
  const handleWristSizeChange = (size) => {
    setUserWristSize(size);
  };

  // Handle continue to product selection - No longer needed since both panels show together
  // const handleContinueToProducts = () => {
  //   setShowWristSizeInput(false);
  //   setShowProductSelection(true);
  // };

  // Handle tab change
  const handleTabChange = (tabName) => {
    setActiveTab(tabName);
  };

  // Handle product selection
  const handleProductSelect = (product) => {
    applyProductToWatchPosition(product.path, activeTab);
  };

  // Initialize camera on component mount
  useEffect(() => {
    initCamera();
  }, []);

  // Hand position monitoring effect (only when autocapture is enabled)
  useEffect(() => {
    if (!isAutoCaptureEnabled || isCaptured) return;

    const interval = setInterval(() => {
      const handInPosition = detectHandInPosition();
      setIsHandInPosition(handInPosition);

      // Only start countdown if hand is properly positioned
      if (handInPosition && !isCountdownActive && countdown === 0) {
        setIsCountdownActive(true);
      }

      // Stop countdown if hand moves out of position
      if (!handInPosition && isCountdownActive) {
        setIsCountdownActive(false);
        setCountdown(0);
      }
    }, 150); // Check every 150ms for better performance

    return () => clearInterval(interval);
  }, [isAutoCaptureEnabled, isCaptured, isCountdownActive, countdown]);

  // Countdown effect (starts automatically when hand is in position)
  useEffect(() => {
    if (!isCountdownActive || isCaptured) {
      setCountdown(0);
      return;
    }

    // Only start countdown if hand is still in position
    if (!isHandInPosition) {
      setIsCountdownActive(false);
      setCountdown(0);
      return;
    }

    // Start countdown
    setCountdown(3);

    const countdownInterval = setInterval(() => {
      setCountdown(prev => {
        // Double-check hand position before continuing countdown
        if (!isHandInPosition) {
          clearInterval(countdownInterval);
          setIsCountdownActive(false);
          return 0;
        }

        if (prev <= 1) {
          // Countdown finished - trigger capture
          clearInterval(countdownInterval);
          setIsCountdownActive(false);
          setIsAutoCaptureEnabled(false); // Turn off autocapture after capture
          handleCapture();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(countdownInterval);
  }, [isCountdownActive, isCaptured, isHandInPosition]);

  // Handle autocapture switch toggle
  const handleAutoCaptureToggle = () => {
    const newState = !isAutoCaptureEnabled;
    setIsAutoCaptureEnabled(newState);

    // Reset states when turning off
    if (!newState) {
      setIsCountdownActive(false);
      setCountdown(0);
      setIsHandInPosition(false);
    }
  };

  // Reset autocapture when going back
  const handleBackWithReset = () => {
    setIsAutoCaptureEnabled(false);
    setIsCountdownActive(false);
    setCountdown(0);
    setIsHandInPosition(false);
    setUserGender('men');
    setUserWristSize(64);
    handleBack();
  };

  // Get current products based on active tab
  const getCurrentProducts = () => {
    return activeTab === 'Watches' ? watches : bracelets;
  };

  return (
    <div
      style={styles.container}
      className={isMobile ? 'mobile-container chrome-mobile-fix' : ''}
    >
      <div style={styles.cameraContainer}>
        <video
          ref={videoRef}
          style={styles.cameraFeed}
          autoPlay
          playsInline
          muted
        />
        <canvas ref={canvasRef} style={{ display: 'none' }} />
        <img
          ref={capturedImageRef}
          style={styles.capturedImage}
          alt="Captured hand"
        />

        {/* Simple Home Button - Only visible when not captured */}
        {!isCaptured && (
          <button
            style={styles.homeBtn}
            onClick={onBackToHome}
            aria-label="Home"
          >
            ←
          </button>
        )}

        {/* Autocapture Switch Button - Only visible when not captured */}
        {!isCaptured && (
          <div style={{ position: 'absolute', top: isMobile ? 10 : 20, right: isMobile ? 10 : 20, zIndex: 20, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <label style={{ color: '#2D8C88', fontWeight: 700, fontSize: isMobile ? 14 : 16, marginBottom: 6, letterSpacing: 0.5 }}>Auto Capture</label>
            <label className="switch">
              <input
                type="checkbox"
                checked={isAutoCaptureEnabled}
                onChange={handleAutoCaptureToggle}
                disabled={isCountdownActive}
                aria-label="Toggle auto capture"
              />
              <span className="slider">
                <span className="circle">
                  <svg className="checkmark" viewBox="0 0 12 10"><polyline points="1.5 6 5 9 10.5 1" fill="none" stroke="#2D8C88" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
                  <svg className="cross" viewBox="0 0 10 10"><line x1="1" y1="1" x2="9" y2="9" stroke="#838383" strokeWidth="2" strokeLinecap="round"/><line x1="9" y1="1" x2="1" y2="9" stroke="#838383" strokeWidth="2" strokeLinecap="round"/></svg>
                </span>
              </span>
            </label>
          </div>
        )}

        {/* Simple Back Button - Only visible when captured */}
        {isCaptured && (
          <button
            style={styles.backBtn}
            onClick={handleBackWithReset}
            aria-label="Back"
          >
            ←
          </button>
        )}

        {/* Countdown Display - Only visible during active countdown */}
        {isCountdownActive && (
          <div style={styles.countdownDisplay}>
            <div style={styles.countdownNumber}>{countdown}</div>
            <div style={styles.countdownText}>Auto capturing...</div>
          </div>
        )}

        {/* Status Messages */}
        {isAutoCaptureEnabled && !isHandInPosition && !isCountdownActive && (
          <div style={styles.statusMessage}>
            <div style={styles.statusText}>Position your arm and wrist in the guide area</div>
            <div style={styles.statusSubtext}>Countdown will start automatically when detected</div>
          </div>
        )}

        {isAutoCaptureEnabled && isHandInPosition && !isCountdownActive && (
          <div style={styles.statusMessage}>
            <div style={{...styles.statusText, backgroundColor: 'rgba(45, 140, 136, 0.9)'}}>
              Perfect! Starting countdown...
            </div>
          </div>
        )}

        {/* Hand Guide SVG */}
        {showHandGuide && (
          <div
            style={{
              ...styles.handGuide,
              opacity: isAutoCaptureEnabled && isHandInPosition ? 0.9 : 0.8,
              filter: isAutoCaptureEnabled && isHandInPosition
                ? 'drop-shadow(0 2px 8px rgba(45, 140, 136, 0.5))'
                : isAutoCaptureEnabled && !isHandInPosition
                  ? 'drop-shadow(0 2px 8px rgba(255, 107, 107, 0.5))'
                  : 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))'
            }}
            className={isMobile ? 'mobile-hand-guide' : ''}
            aria-hidden="true"
          >
            <svg viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
              {/* Wrist guide lines */}
              <path
                d="M100 480 C 150 460, 200 450, 250 450 L 550 450 C 600 450, 650 460, 700 480"
                stroke={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                strokeWidth="5"
                fill="none"
                strokeLinecap="round"
              />
              <path
                d="M100 120 C 150 140, 200 150, 250 150 L 550 150 C 600 150, 650 140, 700 120"
                stroke={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                strokeWidth="5"
                fill="none"
                strokeLinecap="round"
              />
              {/* Wrist/Forearm area (rectangle) */}
              <rect
                x="320"
                y="150"
                width="160"
                height="300"
                fill={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                opacity={isAutoCaptureEnabled && isHandInPosition ? "0.25" : "0.15"}
                rx="15"
                stroke={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                strokeWidth="2"
              />
              {/* Hand area (circle) */}
              <circle
                cx="400"
                cy="300"
                r="60"
                fill={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                opacity={isAutoCaptureEnabled && isHandInPosition ? "0.3" : "0.2"}
                stroke={
                  isAutoCaptureEnabled && isHandInPosition
                    ? "#2D8C88"
                    : isAutoCaptureEnabled && !isHandInPosition
                      ? "#ff6b6b"
                      : "white"
                }
                strokeWidth="2"
              />
              {/* Labels for clarity */}
              {isAutoCaptureEnabled && (
                <>
                  <text x="400" y="140" textAnchor="middle" fill="white" fontSize="14" fontWeight="bold">
                    WRIST & FOREARM
                  </text>
                  <text x="400" y="480" textAnchor="middle" fill="white" fontSize="14" fontWeight="bold">
                    HAND
                  </text>
                </>
              )}
            </svg>
          </div>
        )}

        {/* Product Display */}
        {selectedProduct && (
          <div style={{
            ...styles.productPosition,
            width: activeTab === 'Watches' ? `${WATCH_WIDTH}%` : `${BRACELET_WIDTH}%`,
            height: activeTab === 'Watches' ? `${WATCH_HEIGHT}%` : `${BRACELET_HEIGHT}%`
          }}>
            <div style={{
              position: 'relative',
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <img
                src={typeof selectedProduct === 'object' ? selectedProduct.path : selectedProduct}
                alt="Selected product"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  transform: activeTab === 'Bracelets'
                    ? `rotate(90deg) scale(${BRACELET_HEIGHT / 30})${isRightHand ? ' scaleX(-1)' : ''}`
                    : `scale(${(WATCH_HEIGHT / 25) * (userWristSize / DEFAULT_WRIST_SIZES[userGender])})`,
                  filter: 'drop-shadow(0 2px 8px rgba(0,0,0,0.3))'
                }}
                onLoad={(e) => removeBackground(e.target, activeTab === 'Watches' ? 'watch' : 'bracelet')}
              />
              {activeTab === 'Watches' && typeof selectedProduct === 'object' && (
                <div style={{
                  position: 'absolute',
                  bottom: '-30px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  fontSize: '11px',
                  fontWeight: '600',
                  color: 'white',
                  backgroundColor: 'rgba(45, 140, 136, 0.9)',
                  padding: '3px 8px',
                  borderRadius: '12px',
                  whiteSpace: 'nowrap',
                  pointerEvents: 'none',
                  boxShadow: '0 2px 6px rgba(0,0,0,0.3)',
                  zIndex: 2
                }}>
                  {selectedProduct.dialSize}mm
                  {userWristSize !== DEFAULT_WRIST_SIZES[userGender] && (
                    <span style={{
                      fontSize: '10px',
                      opacity: 0.8,
                      marginLeft: '4px'
                    }}>
                      (scaled {((userWristSize / DEFAULT_WRIST_SIZES[userGender] - 1) * 100).toFixed(0)}%)
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Camera-style Capture Button */}
        <button
          style={styles.captureBtn}
          onClick={handleCapture}
          aria-label={isCaptured ? "Select Products" : "Capture"}
        >
          {!isCaptured ? (
            <div style={styles.captureInner}></div>
          ) : (
            <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
              <path d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"/>
            </svg>
          )}
        </button>

        {/* Reset Button - Only visible when captured */}
        {isCaptured && (
          <button
            style={styles.resetBtn}
            onClick={() => window.location.reload()}
            aria-label="Reset"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
              <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4 7.58 4 12S7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12S8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z"/>
            </svg>
          </button>
        )}
      </div>

      {/* Wrist Size Input Panel - Always visible when captured */}
      {isCaptured && (
        <div
          style={styles.wristSizePanel}
          className={isMobile ? 'mobile-wrist-panel' : ''}
          aria-modal="false"
          role="region"
        >
          <div style={styles.wristSizeContent}>
            <h3 style={styles.wristSizeTitle}>Customize Watch Size</h3>
            <p style={styles.wristSizeSubtitle}>Adjust your wrist size to see how products fit</p>

            {/* Gender Selection */}
            <div style={styles.genderSelection}>
              <button
                style={{
                  ...styles.genderButton,
                  ...(userGender === 'men' ? styles.genderButtonActive : {})
                }}
                onClick={() => handleGenderChange('men')}
              >
                Men (64mm)
              </button>
              <button
                style={{
                  ...styles.genderButton,
                  ...(userGender === 'women' ? styles.genderButtonActive : {})
                }}
                onClick={() => handleGenderChange('women')}
              >
                Women (54mm)
              </button>
            </div>

            {/* Wrist Size Slider */}
            <div style={styles.sliderContainer}>
              <label style={styles.sliderLabel}>
                Wrist Size: {userWristSize}mm
                {userWristSize !== DEFAULT_WRIST_SIZES[userGender] && (
                  <span style={styles.sizeChange}>
                    ({userWristSize > DEFAULT_WRIST_SIZES[userGender] ? '+' : ''}
                    {((userWristSize / DEFAULT_WRIST_SIZES[userGender] - 1) * 100).toFixed(0)}%)
                  </span>
                )}
              </label>
              <input
                type="range"
                min="45"
                max="80"
                value={userWristSize}
                onChange={(e) => handleWristSizeChange(parseInt(e.target.value))}
                style={styles.slider}
              />
              <div style={styles.sliderLabels}>
                <span>45mm</span>
                <span>80mm</span>
              </div>

              {/* Quick Size Presets */}
              <div style={styles.presetButtons}>
                <button
                  style={styles.presetButton}
                  onClick={() => handleWristSizeChange(50)}
                >
                  Small (50mm)
                </button>
                <button
                  style={styles.presetButton}
                  onClick={() => handleWristSizeChange(DEFAULT_WRIST_SIZES[userGender])}
                >
                  Default ({DEFAULT_WRIST_SIZES[userGender]}mm)
                </button>
                <button
                  style={styles.presetButton}
                  onClick={() => handleWristSizeChange(70)}
                >
                  Large (70mm)
                </button>
              </div>
            </div>

            {/* Continue Button removed - products now show automatically */}
          </div>
        </div>
      )}

      {/* Product Selection Panel */}
      {showProductSelection && (
        <div
          style={styles.productSelection}
          className={isMobile ? 'mobile-product-panel' : ''}
          aria-modal="true"
          role="dialog"
        >
          {/* Close button removed - panels always visible when captured */}
          <div style={styles.productTabs}>
            <button
              style={{
                ...styles.tab,
                ...(activeTab === 'Watches' ? styles.activeTab : {})
              }}
              onClick={() => handleTabChange('Watches')}
            >
              Watches
            </button>
            <button
              style={{
                ...styles.tab,
                ...(activeTab === 'Bracelets' ? styles.activeTab : {})
              }}
              onClick={() => handleTabChange('Bracelets')}
            >
              Bracelets
            </button>
          </div>
          <div style={styles.productScroll} className="product-scroll">
            {getCurrentProducts().map((product, index) => {
              // Simple null check only
              if (!product) return null;

              const isSelected = (typeof selectedProduct === 'object' ? selectedProduct?.path : selectedProduct) === product.path;

              return (
                <button
                  key={index}
                  style={{
                    ...styles.productItem,
                    borderColor: isSelected ? '#2D8C88' : '#e9ecef',
                    backgroundColor: isSelected ? '#f0fffe' : '#ffffff'
                  }}
                  title={`${product.name} - ${product.caseDiameter || 'N/A'}mm`}
                  onClick={() => handleProductSelect(product)}
                  aria-label={`Select ${product.name} ${product.caseDiameter || 'N/A'}mm`}
                >
                  <img
                    src={product.path}
                    alt={product.name}
                    style={styles.productImage}
                    onError={(e) => {
                      e.target.parentElement.style.display = 'none';
                    }}
                  />
                  <div style={styles.productLabel}>
                    <div style={styles.productName}>{product.name}</div>
                    {activeTab === 'Watches' && product.caseDiameter && (
                      <div style={styles.productSize}>{product.caseDiameter}mm</div>
                    )}
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

// Styles object - Clean, modern design with improved mobile responsiveness
const styles = {
  container: {
    position: 'relative',
    height: 'calc(var(--vh, 1vh) * 100)',
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: '#f8f9fa',
    color: '#333',
    fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
    overflow: 'hidden',
    touchAction: 'manipulation',
    WebkitTapHighlightColor: 'transparent'
  },
  cameraContainer: {
    flex: 1,
    position: 'relative',
    overflow: 'hidden',
    backgroundColor: '#000',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  cameraFeed: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    transform: 'scaleX(1)' // Fix for Safari mirroring
  },
  capturedImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    objectFit: 'cover',
    display: 'none',
    WebkitTransform: 'scaleX(1)' // Fix for Safari mirroring
  },

  homeBtn: {
    position: 'absolute',
    top: '20px',
    left: '20px',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    color: 'white',
    padding: '12px',
    borderRadius: '50%',
    fontSize: '20px',
    fontWeight: '700',
    cursor: 'pointer',
    zIndex: 10,
    border: 'none',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '44px',
    height: '44px',
    outline: 'none'
  },
  backBtn: {
    position: 'absolute',
    top: '20px',
    left: '20px',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    color: 'white',
    padding: '12px',
    borderRadius: '50%',
    fontSize: '20px',
    fontWeight: '700',
    cursor: 'pointer',
    zIndex: 10,
    border: 'none',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
    transition: 'all 0.2s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '44px',
    height: '44px',
    outline: 'none'
  },
  switchContainer: {
    position: 'absolute',
    top: '20px',
    right: '20px',
    zIndex: 10,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '4px',
    padding: '12px',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: '20px',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)',
    border: '1px solid rgba(255, 255, 255, 0.1)'
  },
  switchTrack: {
    position: 'absolute',
    top: '12px',
    left: '12px',
    width: '60px',
    height: '32px',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: '16px',
    border: '2px solid rgba(255, 255, 255, 0.2)',
    zIndex: 9,
    transition: 'all 0.3s ease'
  },
  switchButton: {
    position: 'relative',
    width: '28px',
    height: '28px',
    borderRadius: '50%',
    border: 'none',
    cursor: 'pointer',
    transition: 'all 0.3s ease',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 11,
    margin: '2px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
    outline: 'none',
    '&:hover': {
      transform: 'scale(1.1)'
    }
  },
  switchLabel: {
    fontSize: '12px',
    fontWeight: '700',
    color: 'white',
    textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)',
    marginTop: '4px',
    padding: '4px 12px',
    borderRadius: '12px',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    letterSpacing: '0.5px'
  },
  countdownDisplay: {
    position: 'absolute',
    top: '35%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 15,
    textAlign: 'center',
    pointerEvents: 'none',
    padding: '16px 24px',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    borderRadius: '20px',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
  },
  countdownNumber: {
    fontSize: '72px',
    fontWeight: '900',
    color: '#2D8C88',
    textShadow: '0 2px 8px rgba(0, 0, 0, 0.5)',
    marginBottom: '8px',
    animation: 'pulse 1s ease-in-out'
  },
  countdownText: {
    fontSize: '16px',
    fontWeight: '600',
    color: 'white',
    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)'
  },
  statusMessage: {
    position: 'absolute',
    top: '25%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 15,
    textAlign: 'center',
    pointerEvents: 'none',
    padding: '12px 20px',
    borderRadius: '16px',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    backdropFilter: 'blur(10px)',
    WebkitBackdropFilter: 'blur(10px)',
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
  },
  statusText: {
    fontSize: '16px',
    fontWeight: '600',
    color: 'white',
    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',
    backgroundColor: 'rgba(255, 107, 107, 0.9)',
    padding: '12px 20px',
    borderRadius: '25px',
    marginBottom: '8px',
    transition: 'all 0.3s ease'
  },
  statusSubtext: {
    fontSize: '12px',
    fontWeight: '500',
    color: 'white',
    textShadow: '0 1px 4px rgba(0, 0, 0, 0.7)',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: '6px 12px',
    borderRadius: '15px'
  },
  handGuide: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '80%',
    maxWidth: '400px',
    height: 'auto',
    opacity: 0.8,
    pointerEvents: 'none',
    zIndex: 5,
    filter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',
    WebkitFilter: 'drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3))',
    transition: 'all 0.3s ease'
  },
  productPosition: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 8,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    pointerEvents: 'none',
    minWidth: '100px', // Ensure minimum size
    minHeight: '100px' // Ensure minimum size
  },
  captureBtn: {
    position: 'absolute',
    bottom: '30px',
    left: '50%',
    transform: 'translateX(-50%)',
    width: '70px',
    height: '70px',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    zIndex: 10,
    transition: 'all 0.2s ease',
    border: '4px solid rgba(255, 255, 255, 0.3)',
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
    outline: 'none',
    padding: 0
  },
  captureInner: {
    width: '50px',
    height: '50px',
    backgroundColor: '#2D8C88',
    borderRadius: '50%',
    transition: 'all 0.2s ease'
  },
  resetBtn: {
    position: 'absolute',
    bottom: '30px',
    right: '30px',
    width: '50px',
    height: '50px',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    zIndex: 10,
    transition: 'all 0.2s ease',
    border: 'none',
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.3)',
    outline: 'none',
    padding: 0
  },

  // Wrist Size Input Panel Styles - Now positioned at top
  wristSizePanel: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(20px)',
    WebkitBackdropFilter: 'blur(20px)',
    borderBottomLeftRadius: '20px',
    borderBottomRightRadius: '20px',
    padding: '16px 24px 12px 24px',
    maxHeight: '35vh',
    display: 'flex',
    flexDirection: 'column',
    zIndex: 25,
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
    border: 'none'
  },
  wristSizeContent: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '12px'
  },
  wristSizeTitle: {
    fontSize: '20px',
    fontWeight: '700',
    color: '#2D8C88',
    margin: 0,
    textAlign: 'center'
  },
  wristSizeSubtitle: {
    fontSize: '14px',
    color: '#666',
    margin: 0,
    textAlign: 'center',
    lineHeight: '1.4'
  },
  genderSelection: {
    display: 'flex',
    gap: '12px',
    width: '100%',
    maxWidth: '300px'
  },
  genderButton: {
    flex: 1,
    padding: '12px 16px',
    borderRadius: '10px',
    fontSize: '14px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: '2px solid #e0e0e0',
    backgroundColor: '#ffffff',
    color: '#666',
    outline: 'none'
  },
  genderButtonActive: {
    backgroundColor: '#2D8C88',
    color: '#ffffff',
    borderColor: '#2D8C88',
    boxShadow: '0 2px 8px rgba(45, 140, 136, 0.3)'
  },
  sliderContainer: {
    width: '100%',
    maxWidth: '300px',
    display: 'flex',
    flexDirection: 'column',
    gap: '8px'
  },
  sliderLabel: {
    fontSize: '16px',
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px'
  },
  sizeChange: {
    fontSize: '14px',
    fontWeight: '500',
    color: '#2D8C88',
    backgroundColor: 'rgba(45, 140, 136, 0.1)',
    padding: '2px 8px',
    borderRadius: '12px'
  },
  slider: {
    width: '100%',
    height: '6px',
    borderRadius: '3px',
    background: '#e0e0e0',
    outline: 'none',
    cursor: 'pointer',
    WebkitAppearance: 'none',
    appearance: 'none'
  },
  sliderLabels: {
    display: 'flex',
    justifyContent: 'space-between',
    fontSize: '12px',
    color: '#999',
    marginTop: '4px'
  },
  presetButtons: {
    display: 'flex',
    gap: '8px',
    width: '100%',
    maxWidth: '300px'
  },
  presetButton: {
    flex: 1,
    padding: '8px 12px',
    borderRadius: '8px',
    fontSize: '12px',
    fontWeight: '500',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: '1px solid #e0e0e0',
    backgroundColor: '#ffffff',
    color: '#666',
    outline: 'none'
  },
  continueButton: {
    width: '100%',
    maxWidth: '300px',
    padding: '14px 24px',
    backgroundColor: '#2D8C88',
    color: '#ffffff',
    borderRadius: '12px',
    fontSize: '16px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: 'none',
    outline: 'none',
    boxShadow: '0 4px 12px rgba(45, 140, 136, 0.3)'
  },

  productSelection: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    backdropFilter: 'blur(20px)',
    WebkitBackdropFilter: 'blur(20px)',
    borderTopLeftRadius: '20px',
    borderTopRightRadius: '20px',
    padding: '16px',
    maxHeight: '55vh', // Reduced to account for wrist size panel at top
    display: 'flex',
    flexDirection: 'column',
    zIndex: 20,
    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',
    border: 'none'
  },
  closeBtn: {
    position: 'absolute',
    top: '12px',
    right: '16px',
    color: '#666',
    cursor: 'pointer',
    zIndex: 21,
    width: '32px',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: '50%',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    transition: 'all 0.2s ease',
    border: 'none',
    outline: 'none',
    padding: 0
  },
  productTabs: {
    display: 'flex',
    marginBottom: '16px',
    marginTop: '8px',
    backgroundColor: '#f0f0f0',
    borderRadius: '10px',
    padding: '3px'
  },
  tab: {
    flex: 1,
    textAlign: 'center',
    padding: '10px 16px',
    borderRadius: '7px',
    fontSize: '15px',
    fontWeight: '600',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    color: '#666',
    outline: 'none',
    border: 'none',
    backgroundColor: 'transparent'
  },
  activeTab: {
    backgroundColor: '#2D8C88',
    color: '#ffffff',
    boxShadow: '0 1px 4px rgba(45, 140, 136, 0.3)'
  },
  productScroll: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fill, minmax(85px, 1fr))',
    gap: '10px',
    maxHeight: 'calc(55vh - 80px)', // Adjusted for reduced panel height
    overflowY: 'auto',
    paddingBottom: '8px',
    scrollbarWidth: 'thin',
    scrollbarColor: '#ddd #f5f5f5'
  },
  productItem: {
    position: 'relative',
    width: '100%',
    aspectRatio: '1/1',
    backgroundColor: '#ffffff',
    borderRadius: '12px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    border: '1px solid #e0e0e0',
    overflow: 'hidden',
    boxShadow: '0 1px 4px rgba(0, 0, 0, 0.1)',
    padding: '6px',
    outline: 'none'
  },
  productImage: {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
    borderRadius: '8px',
    backgroundColor: '#ffffff'
  },
  productLabel: {
    position: 'absolute',
    bottom: '3px',
    left: '3px',
    right: '3px',
    fontSize: '9px',
    color: '#666',
    textAlign: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: '3px',
    padding: '3px 2px',
    overflow: 'hidden'
  },
  productName: {
    fontSize: '9px',
    fontWeight: '600',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    marginBottom: '1px'
  },
  productSize: {
    fontSize: '8px',
    fontWeight: '500',
    color: '#2D8C88',
    whiteSpace: 'nowrap'
  }
};

export default Tryon;